import { getServerSession } from "next-auth/next"
import { NextRequest } from "next/server"

// Import the auth options - we'll need to create this
import { authOptions } from "@/app/api/auth/[...nextauth]/route"

export async function verifyAdmin() {
  const session = await getServerSession(authOptions)
  
  if (!session?.user) {
    throw new Error("Not authenticated")
  }
  
  if (session.user.role !== 'ADMIN') {
    throw new Error("Not authorized")
  }
  
  return { user: session.user }
}

// Alternative version for API routes that need to handle the request object
export async function verifyAdminFromRequest(request: NextRequest) {
  try {
    const { user } = await verifyAdmin()
    return { user }
  } catch (error) {
    return { error: error instanceof Error ? error.message : "Authorization failed" }
  }
}

// Helper function to create standardized error responses
export function createUnauthorizedResponse(message: string = "Not authorized") {
  return new Response(
    JSON.stringify({ error: message }), 
    { 
      status: 403, 
      headers: { 'Content-Type': 'application/json' } 
    }
  )
}

export function createUnauthenticatedResponse(message: string = "Not authenticated") {
  return new Response(
    JSON.stringify({ error: message }), 
    { 
      status: 401, 
      headers: { 'Content-Type': 'application/json' } 
    }
  )
}
