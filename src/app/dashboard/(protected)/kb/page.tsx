import { Database, BookOpen, FileText, Setting<PERSON>, ArrowRight } from "lucide-react"

export default function KnowledgeBasePage() {
  const kbSections = [
    {
      title: "School Information",
      description: "Basic details about your yoga school",
      icon: BookOpen,
      status: "Not configured",
      gradient: "from-blue-500 to-cyan-600"
    },
    {
      title: "Teacher Profiles",
      description: "Information about your instructors",
      icon: FileText,
      status: "Not configured",
      gradient: "from-green-500 to-emerald-600"
    },
    {
      title: "Class Schedules",
      description: "Your yoga class timetables",
      icon: Settings,
      status: "Not configured",
      gradient: "from-purple-500 to-violet-600"
    }
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-cyan-600 to-teal-600 rounded-2xl p-8 text-white">
        <div className="relative z-10">
          <div className="flex items-center space-x-3 mb-4">
            <Database className="h-8 w-8 text-cyan-300" />
            <h1 className="text-4xl font-bold">Knowledge Base</h1>
          </div>
          <p className="text-blue-100 text-lg">
            Configure your chatbot&apos;s knowledge about your yoga school
          </p>
        </div>
        <div className="absolute top-0 right-0 -mt-4 -mr-4 w-32 h-32 bg-white/10 rounded-full"></div>
      </div>

      {/* Knowledge Base Sections */}
      <div>
        <h2 className="text-2xl font-bold text-slate-800 mb-6">Knowledge Sections</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {kbSections.map((section) => (
            <div
              key={section.title}
              className="group relative overflow-hidden bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100"
            >
              <div className="p-6">
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-r ${section.gradient} text-white mb-4`}>
                  <section.icon className="h-6 w-6" />
                </div>
                <h3 className="text-lg font-semibold text-slate-800 mb-2">
                  {section.title}
                </h3>
                <p className="text-slate-600 text-sm mb-4">
                  {section.description}
                </p>
                <div className="flex items-center justify-between">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    {section.status}
                  </span>
                  <ArrowRight className="h-4 w-4 text-slate-400 group-hover:text-slate-600 group-hover:translate-x-1 transition-all duration-200" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Coming Soon Notice */}
      <div className="bg-gradient-to-r from-amber-50 to-yellow-50 border border-amber-200 rounded-2xl p-6">
        <div className="flex items-start space-x-4">
          <div className="flex-shrink-0">
            <Settings className="h-6 w-6 text-amber-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-amber-900 mb-2">
              Coming Soon
            </h3>
            <p className="text-amber-800 text-sm">
              Knowledge Base management will be implemented in Part 2 of the development plan. You&apos;ll be able to configure your school information, teacher profiles, class schedules, and more.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
