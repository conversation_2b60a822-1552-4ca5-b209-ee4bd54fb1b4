import { getServerSession } from "next-auth/next"

export default async function DashboardPage() {
  const session = await getServerSession()

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          Welcome to YogaBot Live
        </h1>
        <p className="text-gray-600 mt-2">
          Manage your AI-powered chatbot for your yoga school
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Knowledge Base
          </h3>
          <p className="text-gray-600 text-sm mb-4">
            Configure your chatbot&apos;s knowledge about your yoga school
          </p>
          <a
            href="/dashboard/kb"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            Manage Knowledge Base
          </a>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Live Chat
          </h3>
          <p className="text-gray-600 text-sm mb-4">
            Monitor active conversations and take over when needed
          </p>
          <a
            href="/dashboard/live"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
          >
            View Live Chats
          </a>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Settings
          </h3>
          <p className="text-gray-600 text-sm mb-4">
            Configure your chatbot appearance and behavior
          </p>
          <a
            href="/dashboard/settings"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gray-600 hover:bg-gray-700"
          >
            Manage Settings
          </a>
        </div>
      </div>

      {session?.user?.role === "ADMIN" && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-red-900 mb-2">
            Admin Panel
          </h3>
          <p className="text-red-700 text-sm mb-4">
            You have administrator access. Manage plans, users, and system settings.
          </p>
          <a
            href="/dashboard/admin"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
          >
            Go to Admin Panel
          </a>
        </div>
      )}
    </div>
  )
}
