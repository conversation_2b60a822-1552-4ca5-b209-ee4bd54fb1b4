import { getServerSession } from "next-auth/next"
import { redirect } from "next/navigation"
import { Sidebar } from "@/components/sidebar"
import { Head<PERSON> } from "@/components/header"
import { authOptions } from "@/lib/auth"

export default async function ProtectedLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const session = await getServerSession(authOptions)

  if (!session) {
    redirect("/login")
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex">
        {/* Sidebar */}
        <Sidebar user={session.user} />
        
        {/* Main content */}
        <div className="flex-1 flex flex-col">
          <Header user={session.user} />
          <main className="flex-1 p-6">
            {children}
          </main>
        </div>
      </div>
    </div>
  )
}
