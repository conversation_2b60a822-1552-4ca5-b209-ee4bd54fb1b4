import { getServerSession } from "next-auth/next"
import { redirect } from "next/navigation"
import { authOptions } from "@/app/api/auth/[...nextauth]/route"

export default async function AdminDashboardPage() {
  const session = await getServerSession(authOptions)

  if (!session || session.user.role !== "ADMIN") {
    redirect("/dashboard")
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          Admin Dashboard
        </h1>
        <p className="text-gray-600 mt-2">
          Manage the YogaBot Live platform
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Subscription Plans
          </h3>
          <p className="text-gray-600 text-sm mb-4">
            Create and manage subscription plans and pricing
          </p>
          <a
            href="/dashboard/admin/plans"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            Manage Plans
          </a>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            User Management
          </h3>
          <p className="text-gray-600 text-sm mb-4">
            Onboard new users and manage existing subscriptions
          </p>
          <a
            href="/dashboard/admin/users"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
          >
            Manage Users
          </a>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Chatbot Management
          </h3>
          <p className="text-gray-600 text-sm mb-4">
            Override chatbot settings and monitor usage
          </p>
          <a
            href="/dashboard/admin/chatbots"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"
          >
            Manage Chatbots
          </a>
        </div>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-yellow-900 mb-2">
          Development Status
        </h3>
        <p className="text-yellow-700 text-sm">
          This is Part 1 of the development plan. User management and chatbot management features will be implemented in the next steps.
        </p>
      </div>
    </div>
  )
}
