"use client"

import { useState, useEffect } from "react"
import { Plus, Edit, Trash2, Users } from "lucide-react"

interface Plan {
  id: string
  name: string
  price: number
  features: {
    chatbotLimit: number
    tokenLimit: number
    kbType: string
    canUseBYOK: boolean
  }
  isActive: boolean
  _count: {
    subscriptions: number
  }
}

export default function AdminPlansPage() {
  const [plans, setPlans] = useState<Plan[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [showCreateForm, setShowCreateForm] = useState(false)

  useEffect(() => {
    fetchPlans()
  }, [])

  const fetchPlans = async () => {
    try {
      const response = await fetch("/api/admin/plans")
      if (!response.ok) {
        throw new Error("Failed to fetch plans")
      }
      const data = await response.json()
      setPlans(data)
    } catch (error) {
      setError("Failed to load plans")
      console.error("Error fetching plans:", error)
    } finally {
      setLoading(false)
    }
  }

  const deletePlan = async (planId: string) => {
    if (!confirm("Are you sure you want to delete this plan?")) {
      return
    }

    try {
      const response = await fetch(`/api/admin/plans/${planId}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to delete plan")
      }

      await fetchPlans() // Refresh the list
    } catch (error) {
      alert(error instanceof Error ? error.message : "Failed to delete plan")
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading plans...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Subscription Plans</h1>
          <p className="text-gray-600 mt-2">
            Manage subscription plans and pricing
          </p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create Plan
        </button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-red-800">{error}</div>
        </div>
      )}

      <div className="bg-white shadow-sm rounded-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Plan
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Price
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Features
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Subscribers
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {plans.map((plan) => (
              <tr key={plan.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    {plan.name}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    ₹{(plan.price / 100).toFixed(2)}
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm text-gray-900">
                    <div>Chatbots: {plan.features.chatbotLimit}</div>
                    <div>Tokens: {plan.features.tokenLimit.toLocaleString()}</div>
                    <div>KB Type: {plan.features.kbType}</div>
                    <div>BYOK: {plan.features.canUseBYOK ? "Yes" : "No"}</div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    plan.isActive 
                      ? "bg-green-100 text-green-800" 
                      : "bg-red-100 text-red-800"
                  }`}>
                    {plan.isActive ? "Active" : "Inactive"}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center text-sm text-gray-900">
                    <Users className="h-4 w-4 mr-1" />
                    {plan._count.subscriptions}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex items-center space-x-2">
                    <button className="text-indigo-600 hover:text-indigo-900">
                      <Edit className="h-4 w-4" />
                    </button>
                    <button 
                      onClick={() => deletePlan(plan.id)}
                      className="text-red-600 hover:text-red-900"
                      disabled={plan._count.subscriptions > 0}
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {plans.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500">No plans found</div>
          </div>
        )}
      </div>

      {showCreateForm && (
        <CreatePlanModal 
          onClose={() => setShowCreateForm(false)}
          onSuccess={() => {
            setShowCreateForm(false)
            fetchPlans()
          }}
        />
      )}
    </div>
  )
}

// Simple modal component for creating plans
function CreatePlanModal({ onClose, onSuccess }: { onClose: () => void, onSuccess: () => void }) {
  const [formData, setFormData] = useState({
    name: "",
    price: "",
    chatbotLimit: "1",
    tokenLimit: "50000",
    kbType: "simple",
    canUseBYOK: false,
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError("")

    try {
      const response = await fetch("/api/admin/plans", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: formData.name,
          price: parseInt(formData.price) * 100, // Convert to cents
          features: {
            chatbotLimit: parseInt(formData.chatbotLimit),
            tokenLimit: parseInt(formData.tokenLimit),
            kbType: formData.kbType,
            canUseBYOK: formData.canUseBYOK,
          },
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to create plan")
      }

      onSuccess()
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to create plan")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-md w-full p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Create New Plan</h3>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Plan Name</label>
            <input
              type="text"
              required
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Price (₹)</label>
            <input
              type="number"
              required
              min="0"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
              value={formData.price}
              onChange={(e) => setFormData({ ...formData, price: e.target.value })}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Chatbot Limit</label>
            <input
              type="number"
              required
              min="1"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
              value={formData.chatbotLimit}
              onChange={(e) => setFormData({ ...formData, chatbotLimit: e.target.value })}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Token Limit</label>
            <input
              type="number"
              required
              min="1000"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
              value={formData.tokenLimit}
              onChange={(e) => setFormData({ ...formData, tokenLimit: e.target.value })}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Knowledge Base Type</label>
            <select
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
              value={formData.kbType}
              onChange={(e) => setFormData({ ...formData, kbType: e.target.value })}
            >
              <option value="simple">Simple</option>
              <option value="structured">Structured</option>
            </select>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="canUseBYOK"
              className="h-4 w-4 text-indigo-600 border-gray-300 rounded"
              checked={formData.canUseBYOK}
              onChange={(e) => setFormData({ ...formData, canUseBYOK: e.target.checked })}
            />
            <label htmlFor="canUseBYOK" className="ml-2 block text-sm text-gray-900">
              Allow Bring Your Own Key (BYOK)
            </label>
          </div>

          {error && (
            <div className="text-red-600 text-sm">{error}</div>
          )}

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 rounded-md disabled:opacity-50"
            >
              {loading ? "Creating..." : "Create Plan"}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
