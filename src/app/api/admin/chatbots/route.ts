import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { PrismaClient } from "@prisma/client"
import { verifyAdmin } from "@/lib/admin-auth"

const prisma = new PrismaClient()

// Schema for chatbot settings override
const chatbotUpdateSchema = z.object({
  systemPrompt: z.string().optional(),
  llmProvider: z.string().optional(),
  llmModel: z.string().optional(),
  encryptedLlmApiKey: z.string().optional(),
  kbTypeOverride: z.enum(["simple", "structured"]).optional(),
  simpleKbCharacterLimit: z.number().int().min(1000).optional(),
})

// GET /api/admin/chatbots - List all chatbots with user info
export async function GET() {
  try {
    await verifyAdmin()

    const chatbots = await prisma.chatbot.findMany({
      orderBy: { createdAt: 'desc' },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            subscription: {
              include: {
                plan: true
              }
            }
          }
        },
        _count: {
          select: {
            chatSessions: true,
            knowledgeChunks: true
          }
        }
      }
    })

    return NextResponse.json(chatbots)
  } catch (error) {
    if (error instanceof Error && error.message === "Not authorized") {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 })
    }
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    
    console.error("Error fetching chatbots:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
