import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { PrismaClient } from "@prisma/client"
import { verifyAdmin } from "@/lib/admin-auth"

const prisma = new PrismaClient()

// Schema for updating user subscription
const updateUserSchema = z.object({
  name: z.string().min(1, "Name is required").optional(),
  planId: z.string().min(1, "Plan selection is required").optional(),
  subscriptionStatus: z.enum(["active", "cancelled", "past_due"]).optional(),
  currentPeriodEnd: z.string().transform((str) => new Date(str)).optional(),
})

// GET /api/admin/users/[userId] - Get specific user details
export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    await verifyAdmin()

    const { userId } = params

    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        subscription: {
          include: {
            plan: true
          }
        },
        chatbots: {
          select: {
            id: true,
            approvedDomain: true,
            createdAt: true,
          }
        }
      }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    return NextResponse.json(user)
  } catch (error) {
    if (error instanceof Error && error.message === "Not authorized") {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 })
    }
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    
    console.error("Error fetching user:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// PUT /api/admin/users/[userId] - Update user and subscription
export async function PUT(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    await verifyAdmin()

    const { userId } = params
    const body = await request.json()
    const validatedData = updateUserSchema.parse(body)

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        subscription: true
      }
    })

    if (!existingUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // If plan is being updated, check if it exists
    if (validatedData.planId) {
      const plan = await prisma.plan.findUnique({
        where: { id: validatedData.planId }
      })

      if (!plan) {
        return NextResponse.json(
          { error: "Selected plan not found" }, 
          { status: 400 }
        )
      }
    }

    // Update user and subscription in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update user if name is provided
      let updatedUser = existingUser
      if (validatedData.name) {
        updatedUser = await tx.user.update({
          where: { id: userId },
          data: { name: validatedData.name }
        })
      }

      // Update subscription if any subscription data is provided
      let updatedSubscription = existingUser.subscription
      if (validatedData.planId || validatedData.subscriptionStatus || validatedData.currentPeriodEnd) {
        if (existingUser.subscription) {
          updatedSubscription = await tx.subscription.update({
            where: { userId: userId },
            data: {
              ...(validatedData.planId && { planId: validatedData.planId }),
              ...(validatedData.subscriptionStatus && { status: validatedData.subscriptionStatus }),
              ...(validatedData.currentPeriodEnd && { currentPeriodEnd: validatedData.currentPeriodEnd }),
            }
          })
        }
      }

      return { user: updatedUser, subscription: updatedSubscription }
    })

    // Return updated user with subscription details
    const userWithSubscription = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        subscription: {
          include: {
            plan: true
          }
        }
      }
    })

    return NextResponse.json(userWithSubscription)
  } catch (error) {
    if (error instanceof Error && error.message === "Not authorized") {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 })
    }
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors }, 
        { status: 400 }
      )
    }
    
    console.error("Error updating user:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// DELETE /api/admin/users/[userId] - Delete user (with safeguards)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    await verifyAdmin()

    const { userId } = params

    // Check if user exists and get their data
    const existingUser = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        chatbots: true,
        subscription: true
      }
    })

    if (!existingUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Prevent deletion of admin users
    if (existingUser.role === 'ADMIN') {
      return NextResponse.json(
        { error: "Cannot delete admin users" }, 
        { status: 400 }
      )
    }

    // Check if user has active chatbots
    if (existingUser.chatbots.length > 0) {
      return NextResponse.json(
        { error: "Cannot delete user with active chatbots. Please delete chatbots first." }, 
        { status: 400 }
      )
    }

    // Delete user (subscription will be deleted due to cascade)
    await prisma.user.delete({
      where: { id: userId }
    })

    return NextResponse.json({ message: "User deleted successfully" })
  } catch (error) {
    if (error instanceof Error && error.message === "Not authorized") {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 })
    }
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    
    console.error("Error deleting user:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
