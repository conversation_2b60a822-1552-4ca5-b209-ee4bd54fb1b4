"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { 
  Home, 
  Settings, 
  Users, 
  Bot, 
  Database,
  MessageSquare,
  BarChart3,
  Crown
} from "lucide-react"

interface SidebarProps {
  user: {
    id: string
    email: string
    name?: string | null
    role: string
  }
}

export function Sidebar({ user }: SidebarProps) {
  const pathname = usePathname()

  const navigation = [
    { name: "Dashboard", href: "/dashboard", icon: Home },
    { name: "Knowledge Base", href: "/dashboard/kb", icon: Database },
    { name: "Live Chat", href: "/dashboard/live", icon: MessageSquare },
    { name: "Leads", href: "/dashboard/leads", icon: BarChart3 },
    { name: "Setting<PERSON>", href: "/dashboard/settings", icon: Settings },
  ]

  const adminNavigation = [
    { name: "Admin Dashboard", href: "/dashboard/admin", icon: Crown },
    { name: "Plans", href: "/dashboard/admin/plans", icon: Settings },
    { name: "Users", href: "/dashboard/admin/users", icon: Users },
    { name: "Chatbot<PERSON>", href: "/dashboard/admin/chatbots", icon: Bo<PERSON> },
  ]

  return (
    <div className="w-64 bg-white shadow-sm border-r border-gray-200">
      <div className="p-6">
        <h1 className="text-xl font-bold text-gray-900">YogaBot Live</h1>
        <p className="text-sm text-gray-500 mt-1">{user.email}</p>
      </div>

      <nav className="mt-6">
        <div className="px-3">
          <ul className="space-y-1">
            {navigation.map((item) => {
              const isActive = pathname === item.href
              return (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className={`
                      group flex items-center px-3 py-2 text-sm font-medium rounded-md
                      ${isActive 
                        ? "bg-indigo-50 text-indigo-700" 
                        : "text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                      }
                    `}
                  >
                    <item.icon
                      className={`
                        mr-3 h-5 w-5 flex-shrink-0
                        ${isActive ? "text-indigo-500" : "text-gray-400 group-hover:text-gray-500"}
                      `}
                    />
                    {item.name}
                  </Link>
                </li>
              )
            })}
          </ul>
        </div>

        {user.role === "ADMIN" && (
          <div className="mt-8 px-3">
            <h3 className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
              Admin
            </h3>
            <ul className="mt-2 space-y-1">
              {adminNavigation.map((item) => {
                const isActive = pathname === item.href
                return (
                  <li key={item.name}>
                    <Link
                      href={item.href}
                      className={`
                        group flex items-center px-3 py-2 text-sm font-medium rounded-md
                        ${isActive 
                          ? "bg-red-50 text-red-700" 
                          : "text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                        }
                      `}
                    >
                      <item.icon
                        className={`
                          mr-3 h-5 w-5 flex-shrink-0
                          ${isActive ? "text-red-500" : "text-gray-400 group-hover:text-gray-500"}
                        `}
                      />
                      {item.name}
                    </Link>
                  </li>
                )
              })}
            </ul>
          </div>
        )}
      </nav>
    </div>
  )
}
