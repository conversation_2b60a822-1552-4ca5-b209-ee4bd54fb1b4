// This is your Prisma schema file.

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("POSTGRES_PRISMA_URL")
}

// ===================================
// 1. Core SaaS & User Models
// ===================================

model User {
  id           String        @id @default(cuid())
  email        String        @unique
  name         String?
  password     String        // Stores a secure hash from bcrypt
  role         Role          @default(USER)
  chatbots     Chatbot[]     // A user can now have multiple chatbots
  subscription Subscription?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum Role {
  ADMIN
  USER
}

model Plan {
  id        String   @id @default(cuid())
  name      String   @unique // e.g., "Basic", "Pro", "Enterprise"
  price     Int      // Price in cents
  features  Json     // Defines capabilities: { chatbotLimit: 1, tokenLimit: 50000, kbType: "simple", canUseBYOK: false, ... }
  isActive  Boolean  @default(true)

  subscriptions Subscription[]
}

model Subscription {
  id                     String    @id @default(cuid())
  userId                 String    @unique
  user                   User      @relation(fields: [userId], references: [id])
  planId                 String
  plan                   Plan      @relation(fields: [planId], references: [id])
  status                 String    // e.g., "active", "cancelled", "past_due"
  currentPeriodEnd       DateTime
  razorpaySubscriptionId String?   @unique

  // Usage Tracking
  tokensUsedThisPeriod   Int       @default(0)
  sessionsThisPeriod     Int       @default(0)
}

// ===================================
// 2. Chatbot & Prompt Configuration
// ===================================

model Chatbot {
  id                    String    @id @default(cuid())
  userId                String
  user                  User      @relation(fields: [userId], references: [id])
  approvedDomain        String

  // Admin-controlled LLM settings & Overrides
  systemPrompt          String?   @db.Text
  llmProvider           String    @default("gemini")
  llmModel              String    @default("gemini-pro")
  encryptedLlmApiKey    String?   // For BYOK (Bring Your Own Key)
  kbTypeOverride        String?   // Admin override: "simple" or "structured"
  simpleKbCharacterLimit Int?     // Admin override for character limit on simple KB

  // User-controlled settings
  widgetConfig          Json?
  smtpConfig            Json?     // Encrypted SMTP credentials
  personas              Persona[]

  // Knowledge Base Data
  simpleKbText          String?   @db.Text // For "simple" plan users
  structuredKbBrand     SchoolBrand?
  structuredKbContact   SchoolContact?
  structuredKbTeachers  Teacher[]
  structuredKbTtcs      TTC[]
  structuredKbRetreats  Retreat[]
  structuredKbPolicies  Policy[]
  structuredKbFaqs      FAQ[]

  // Searchable Data & History
  knowledgeChunks       KnowledgeBaseChunk[]
  chatSessions          ChatSession[]
}

model Persona {
  id          String   @id @default(cuid())
  chatbotId   String
  chatbot     Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  name        String
  personaText String   @db.Text
  isActive    Boolean  @default(true)
}

// This model holds the final, searchable data generated from THE STRUCTURED KB ONLY.
model KnowledgeBaseChunk {
  id                 String   @id @default(cuid())
  chatbotId          String
  chatbot            Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  content            String   @db.Text
  embedding          Unsupported("vector(384)")? // Optional, not used for FTS query-time retrieval
  content_tsvector   Unsupported("tsvector")? // Special column for Full-Text Search

  source             String?  // e.g., "TTC: 200-Hour Foundational" to identify the source

  @@index([content_tsvector], map: "content_tsvector_idx", type: Gin) // CRITICAL for FTS performance
}

// ===================================
// 3. Structured Knowledge Base Models
// (Unchanged from previous versions)
// ===================================

model SchoolBrand {
  id              String   @id @default(cuid())
  chatbotId       String   @unique
  chatbot         Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  schoolName      String
  tagline         String?
  schoolType      String?
  yogaStylesTaught String[]
  missionStatement String?   @db.Text
  aboutTheSchool  String?   @db.Text
  founderInfo     String?   @db.Text
}

model SchoolContact {
  id              String   @id @default(cuid())
  chatbotId       String   @unique
  chatbot         Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  fullAddress     String?
  googleMapsLink  String?
  howToReach      String?   @db.Text
  primaryPhone    String?
  whatsappNumber  String?
  primaryEmail    String?
  websiteUrl      String?
  socialMediaLinks Json?     // [{ platform: 'Instagram', url: '...' }]
}

model Teacher {
  id              String   @id @default(cuid())
  chatbotId       String
  chatbot         Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  name            String
  role            String?
  photoUrl        String?
  bio             String?   @db.Text
  certifications  String[]
}

model TTC {
  id                  String   @id @default(cuid())
  chatbotId           String
  chatbot             Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  name                String
  certificationBody   String?
  summary             String?   @db.Text
  duration            String?
  skillLevel          String?
  curriculumDetails   String?   @db.Text
  sampleDailySchedule String?   @db.Text
  priceOptions        Json     // [{ type: 'Shared Twin', price: 1800 }, ...]
  inclusions          String[]
  exclusions          String[]
  upcomingDates       Json     // [{ start: '...', end: '...', status: 'Open' }]
  applicationProcess  String?   @db.Text
}

model Retreat {
  id                  String   @id @default(cuid())
  chatbotId           String
  chatbot             Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  name                String
  theme               String?
  duration            String?
  intendedAudience    String?
  highlights          String[]
  priceOptions        Json
  upcomingDates       Json
}

model Policy {
  id                            String   @id @default(cuid())
  chatbotId                     String   @unique
  chatbot                       Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  codeOfConduct                 String?   @db.Text
  paymentPolicy                 String?   @db.Text
  cancellationAndRefundPolicy   String?   @db.Text
}

model FAQ {
  id          String   @id @default(cuid())
  chatbotId   String
  chatbot     Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  question    String
  answer      String   @db.Text
}


// ===================================
// 4. Chat History & Visitor Models
// (Unchanged from previous versions)
// ===================================

model ChatSession {
  id            String    @id @default(cuid())
  chatbotId     String
  chatbot       Chatbot   @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  visitorId     String
  visitor       Visitor   @relation(fields: [visitorId], references: [id])
  controller    String    @default("LLM") // Can be "LLM" or "USER"
  ablyChannel   String    @unique
  tokenCount    Int       @default(0)

  messages      Message[]
  createdAt     DateTime  @default(now())
}

model Message {
  id            String      @id @default(cuid())
  chatSessionId String
  chatSession   ChatSession @relation(fields: [chatSessionId], references: [id], onDelete: Cascade)
  senderType    String      // "VISITOR", "LLM", or "USER"
  content       String      @db.Text
  systemData    Json?       // For debugging: stores raw LLM tool calls, prompts, etc.
  createdAt     DateTime    @default(now())
}

model Visitor {
  id           String        @id @default(cuid())
  email        String?
  name         String?
  profileData  Json?
  chatSessions ChatSession[]
}
